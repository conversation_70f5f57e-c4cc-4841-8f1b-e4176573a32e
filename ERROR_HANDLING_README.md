# 🛡️ Comprehensive Error Handling System

## 📋 Tổng quan

Hệ thống xử lý lỗi toàn diện cho ứng dụng React với các tính năng:

- ✅ **Error Logging**: Ghi log có cấu trúc với nhiều mức độ nghiêm trọng
- ✅ **User-Friendly Display**: Hi<PERSON>n thị lỗi thân thiện với người dùng
- ✅ **Fallback Mechanisms**: Cơ chế dự phòng và khôi phục
- ✅ **Global Error Handling**: Xử lý lỗi toàn cục và tự động

## 🚀 Cách sử dụng

### 1. Error Context và Hooks

```typescript
import { useError, useErrorHandler } from '@/contexts/error-context';

function MyComponent() {
  const { errorState, addError, clearErrors } = useError();
  const { handleError } = useErrorHandler('MyComponent');
  
  const handleAction = async () => {
    try {
      await someApiCall();
    } catch (error) {
      handleError(error, 'api_call_failed');
    }
  };
}
```

### 2. API Calls với Retry tự động

```typescript
import { apiClient } from '@/lib/api-interceptor';

// Tự động retry và xử lý lỗi
const data = await apiClient.get('/api/data');
const result = await apiClient.post('/api/users', userData);
```

### 3. Error Boundaries

```typescript
import { ErrorBoundary, withErrorBoundary } from '@/components/error';

// Sử dụng trực tiếp
<ErrorBoundary level="section" name="MySection">
  <MyComponent />
</ErrorBoundary>

// Hoặc sử dụng HOC
export default withErrorBoundary(MyComponent, {
  level: 'component',
  name: 'MyComponent'
});
```

### 4. Fallback Components

```typescript
import { DataFallback, ImageFallback, NetworkFallback } from '@/components/fallback';

// Data fallback với loading và empty states
<DataFallback
  isLoading={loading}
  isEmpty={data.length === 0}
  error={error}
  onRetry={refetch}
>
  {data.map(item => <Item key={item.id} {...item} />)}
</DataFallback>

// Image fallback
<ImageFallback
  src={imageUrl}
  alt="Description"
  fallbackSrc="/placeholder.jpg"
  className="w-32 h-32"
/>

// Network fallback
<NetworkFallback>
  <OnlineOnlyContent />
</NetworkFallback>
```

### 5. Retry Logic

```typescript
import { useRetry, withRetry } from '@/lib/retry';

// Sử dụng hook
const { execute, isRetrying } = useRetry(
  async () => await apiCall(),
  { maxAttempts: 3, enabled: true }
);

// Sử dụng trực tiếp
const result = await withRetry(
  () => apiCall(),
  { maxAttempts: 3, baseDelay: 1000 }
);
```

### 6. Offline Support

```typescript
import { useOffline, useOfflineQueue } from '@/hooks/use-offline';

const { isOnline, isOffline, checkConnectivity } = useOffline();
const { addToQueue, processQueue } = useOfflineQueue();

// Thêm action vào queue khi offline
if (isOffline) {
  addToQueue({
    type: 'CREATE_USER',
    payload: userData,
    maxRetries: 3
  });
}
```

### 7. Graceful Degradation

```typescript
import { useGracefulDegradation, COMMON_FEATURES } from '@/hooks/use-graceful-degradation';

const { currentLevel, isFeatureAvailable } = useGracefulDegradation({
  features: [COMMON_FEATURES.api, COMMON_FEATURES.storage]
});

if (isFeatureAvailable('notifications')) {
  // Hiển thị notifications
} else {
  // Fallback to console logging
}
```

## 🎯 Error Types

### Built-in Error Classes

```typescript
import {
  AppError,
  ValidationError,
  NetworkError,
  ServerError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  TimeoutError
} from '@/lib/error-handling';

// Validation error
throw new ValidationError('Email is required');

// Network error
throw new NetworkError('Connection failed');

// Custom error với context
throw new AppError(
  'Custom error message',
  'CUSTOM_ERROR_CODE',
  500,
  ErrorSeverity.HIGH,
  ErrorCategory.SERVER,
  context
);
```

### Error Severity Levels

- `LOW`: Lỗi nhỏ, không ảnh hưởng nhiều
- `MEDIUM`: Lỗi trung bình, cần chú ý
- `HIGH`: Lỗi nghiêm trọng, cần xử lý ngay
- `CRITICAL`: Lỗi cực kỳ nghiêm trọng, có thể crash app

## 🔧 Configuration

### Error Management Provider

```typescript
// app/layout.tsx
<ErrorManagementProvider
  config={{
    enableErrorReporting: true,
    enableNetworkDetection: true,
    enableApiInterception: true,
    errorReportingEndpoint: '/api/errors',
    environment: process.env.NODE_ENV,
    buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION,
  }}
>
  <App />
</ErrorManagementProvider>
```

### Error Logger Configuration

```typescript
import { errorLogger } from '@/lib/error-handling';

errorLogger.configure({
  enableConsoleLogging: true,
  enableRemoteLogging: true,
  remoteEndpoint: '/api/logs',
  maxLogs: 1000
});
```

## 📊 Monitoring và Analytics

### Error Reporting

```typescript
import { reportError } from '@/lib/error-reporting';

// Report error manually
await reportError(error, {
  userId: user.id,
  action: 'button_click',
  additionalContext: { buttonId: 'submit' }
});
```

### Error Statistics

```typescript
import { errorLogger } from '@/lib/error-handling';

// Lấy logs theo level
const errorLogs = errorLogger.getLogs('ERROR', 50);
const allLogs = errorLogger.getLogs();

// Export logs
const logsJson = errorLogger.exportLogs();
```

## 🧪 Testing

Truy cập `/error-test` để test các tính năng:

- Trigger các loại lỗi khác nhau
- Test error boundaries
- Test retry mechanisms
- Test offline functionality
- Test fallback components

## 📁 File Structure

```
src/
├── lib/
│   ├── error-handling.ts          # Core error classes
│   ├── retry.ts                   # Retry logic
│   ├── api-interceptor.ts         # HTTP error handling
│   ├── form-error-handler.ts      # Form validation
│   ├── network-detector.ts        # Network status
│   ├── error-reporting.ts         # Error reporting
│   └── error-management.ts        # Main exports
├── contexts/
│   └── error-context.tsx          # Error state management
├── components/
│   ├── error/                     # Error UI components
│   └── fallback/                  # Fallback components
├── hooks/
│   ├── use-offline.ts             # Offline detection
│   └── use-graceful-degradation.ts # Feature degradation
└── providers/
    └── error-management-provider.tsx # Global provider
```

## 🔍 Best Practices

1. **Luôn sử dụng Error Boundaries** cho các component quan trọng
2. **Wrap API calls** trong try-catch và sử dụng error handlers
3. **Provide fallback UI** cho tất cả các trạng thái lỗi
4. **Log errors** với đủ context để debug
5. **Test error scenarios** thường xuyên
6. **Monitor error rates** và patterns
7. **Provide recovery actions** khi có thể

## 🚨 Troubleshooting

### Common Issues

1. **Error boundaries không catch async errors**: Sử dụng error handlers trong async functions
2. **Toast notifications không hiện**: Kiểm tra ErrorToastContainer đã được add vào layout
3. **Retry không hoạt động**: Kiểm tra error có retryable không
4. **Offline queue không process**: Kiểm tra network detection và queue processing

### Debug Mode

Trong development mode, có thể access global utilities:

```javascript
// Browser console
window.__errorManagement.triggerTestError();
window.__errorManagement.errorLogger.getLogs();
```
