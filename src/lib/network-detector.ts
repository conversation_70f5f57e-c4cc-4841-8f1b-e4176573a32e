'use client';

import { useCallback, useEffect, useState } from 'react';
import { errorLogger } from './error-handling';

// ============================================================================
// TYPES
// ============================================================================

export interface NetworkInfo {
	isOnline: boolean;
	connectionType: string | null;
	effectiveType: string | null;
	downlink: number | null;
	rtt: number | null;
	saveData: boolean;
}

export interface NetworkDetectorOptions {
	pingUrl?: string;
	pingInterval?: number;
	pingTimeout?: number;
	onOnline?: (info: NetworkInfo) => void;
	onOffline?: (info: NetworkInfo) => void;
	onSlowConnection?: (info: NetworkInfo) => void;
}

export interface NetworkThresholds {
	slowDownlink: number; // Mbps
	slowRtt: number; // ms
}

// ============================================================================
// NETWORK DETECTOR CLASS
// ============================================================================

export class NetworkDetector {
	private options: Required<NetworkDetectorOptions>;
	private thresholds: NetworkThresholds;
	private pingInterval: NodeJS.Timeout | null = null;
	private listeners: Set<(info: NetworkInfo) => void> = new Set();
	private currentInfo: NetworkInfo;

	constructor(options: NetworkDetectorOptions = {}) {
		this.options = {
			pingUrl: '/api/ping',
			pingInterval: 30000, // 30 seconds
			pingTimeout: 5000, // 5 seconds
			onOnline: () => {},
			onOffline: () => {},
			onSlowConnection: () => {},
			...options,
		};

		this.thresholds = {
			slowDownlink: 1.0, // 1 Mbps
			slowRtt: 1000, // 1 second
		};

		this.currentInfo = this.getNetworkInfo();
		this.setupEventListeners();
		this.startPinging();
	}

	// Get current network information
	getNetworkInfo(): NetworkInfo {
		if (typeof navigator === 'undefined') {
			return {
				isOnline: true,
				connectionType: null,
				effectiveType: null,
				downlink: null,
				rtt: null,
				saveData: false,
			};
		}

		const connection =
			(navigator as any).connection ||
			(navigator as any).mozConnection ||
			(navigator as any).webkitConnection;

		return {
			isOnline: navigator.onLine,
			connectionType: connection?.type || null,
			effectiveType: connection?.effectiveType || null,
			downlink: connection?.downlink || null,
			rtt: connection?.rtt || null,
			saveData: connection?.saveData || false,
		};
	}

	// Add listener for network changes
	addListener(callback: (info: NetworkInfo) => void): () => void {
		this.listeners.add(callback);

		// Return unsubscribe function
		return () => {
			this.listeners.delete(callback);
		};
	}

	// Check if connection is slow
	isSlowConnection(info: NetworkInfo = this.currentInfo): boolean {
		if (info.downlink !== null && info.downlink < this.thresholds.slowDownlink) {
			return true;
		}

		if (info.rtt !== null && info.rtt > this.thresholds.slowRtt) {
			return true;
		}

		return info.effectiveType === 'slow-2g' || info.effectiveType === '2g';
	}

	// Ping server to verify connectivity
	async pingServer(): Promise<boolean> {
		try {
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), this.options.pingTimeout);

			const startTime = performance.now();
			const response = await fetch(this.options.pingUrl, {
				method: 'HEAD',
				signal: controller.signal,
				cache: 'no-cache',
			});

			clearTimeout(timeoutId);
			const endTime = performance.now();
			const rtt = endTime - startTime;

			// Update RTT if we don't have connection API data
			if (this.currentInfo.rtt === null) {
				this.currentInfo = {
					...this.currentInfo,
					rtt,
				};
			}

			const isOnline = response.ok;

			errorLogger.debug(
				'Server ping completed',
				{ isOnline, rtt, status: response.status },
				'NetworkDetector'
			);

			return isOnline;
		} catch (error) {
			errorLogger.debug(
				'Server ping failed',
				{ error, pingUrl: this.options.pingUrl },
				'NetworkDetector'
			);
			return false;
		}
	}

	// Update network information and notify listeners
	private updateNetworkInfo(source: string): void {
		const newInfo = this.getNetworkInfo();
		const wasOnline = this.currentInfo.isOnline;
		const isOnline = newInfo.isOnline;

		// Check if status changed
		if (wasOnline !== isOnline) {
			errorLogger.info(
				`Network status changed to ${isOnline ? 'online' : 'offline'}`,
				{ source, oldInfo: this.currentInfo, newInfo },
				'NetworkDetector'
			);

			if (isOnline) {
				this.options.onOnline(newInfo);
			} else {
				this.options.onOffline(newInfo);
			}
		}

		// Check for slow connection
		if (isOnline && this.isSlowConnection(newInfo)) {
			this.options.onSlowConnection(newInfo);
		}

		this.currentInfo = newInfo;

		// Notify all listeners
		this.listeners.forEach((callback) => {
			try {
				callback(newInfo);
			} catch (error) {
				errorLogger.error(
					'Network listener callback failed',
					error instanceof Error ? error : new Error(String(error)),
					{ newInfo },
					'NetworkDetector'
				);
			}
		});
	}

	// Setup browser event listeners
	private setupEventListeners(): void {
		if (typeof window === 'undefined') return;

		const handleOnline = () => this.updateNetworkInfo('browser_online_event');
		const handleOffline = () => this.updateNetworkInfo('browser_offline_event');
		const handleConnectionChange = () => this.updateNetworkInfo('connection_change_event');

		window.addEventListener('online', handleOnline);
		window.addEventListener('offline', handleOffline);

		// Listen for connection changes
		const connection =
			(navigator as any).connection ||
			(navigator as any).mozConnection ||
			(navigator as any).webkitConnection;

		if (connection) {
			connection.addEventListener('change', handleConnectionChange);
		}

		// Store cleanup function
		this.cleanup = () => {
			window.removeEventListener('online', handleOnline);
			window.removeEventListener('offline', handleOffline);

			if (connection) {
				connection.removeEventListener('change', handleConnectionChange);
			}
		};
	}

	// Start periodic server pinging
	private startPinging(): void {
		if (this.options.pingInterval <= 0) return;

		this.pingInterval = setInterval(async () => {
			if (!this.currentInfo.isOnline) return; // Skip if already offline

			const serverReachable = await this.pingServer();

			if (!serverReachable && this.currentInfo.isOnline) {
				// Server not reachable but browser thinks we're online
				this.currentInfo = {
					...this.currentInfo,
					isOnline: false,
				};

				this.options.onOffline(this.currentInfo);
				this.listeners.forEach((callback) => callback(this.currentInfo));
			} else if (serverReachable && !this.currentInfo.isOnline) {
				// Server reachable and we thought we were offline
				this.updateNetworkInfo('server_ping');
			}
		}, this.options.pingInterval);
	}

	// Cleanup function
	private cleanup: (() => void) | null = null;

	// Destroy the detector
	destroy(): void {
		if (this.pingInterval) {
			clearInterval(this.pingInterval);
			this.pingInterval = null;
		}

		if (this.cleanup) {
			this.cleanup();
			this.cleanup = null;
		}

		this.listeners.clear();
	}

	// Get current network status
	getCurrentInfo(): NetworkInfo {
		return { ...this.currentInfo };
	}

	// Update thresholds for slow connection detection
	updateThresholds(thresholds: Partial<NetworkThresholds>): void {
		this.thresholds = { ...this.thresholds, ...thresholds };
	}
}

// ============================================================================
// REACT HOOK
// ============================================================================

export function useNetworkDetector(options?: NetworkDetectorOptions) {
	const [networkInfo, setNetworkInfo] = useState<NetworkInfo>(() => {
		if (typeof navigator === 'undefined') {
			return {
				isOnline: true,
				connectionType: null,
				effectiveType: null,
				downlink: null,
				rtt: null,
				saveData: false,
			};
		}

		const connection =
			(navigator as any).connection ||
			(navigator as any).mozConnection ||
			(navigator as any).webkitConnection;

		return {
			isOnline: navigator.onLine,
			connectionType: connection?.type || null,
			effectiveType: connection?.effectiveType || null,
			downlink: connection?.downlink || null,
			rtt: connection?.rtt || null,
			saveData: connection?.saveData || false,
		};
	});

	const [detector] = useState(() => new NetworkDetector(options));

	useEffect(() => {
		const unsubscribe = detector.addListener(setNetworkInfo);

		// Set initial state
		setNetworkInfo(detector.getCurrentInfo());

		return () => {
			unsubscribe();
			detector.destroy();
		};
	}, [detector]);

	const checkConnectivity = useCallback(async () => {
		const isOnline = await detector.pingServer();
		return isOnline;
	}, [detector]);

	const isSlowConnection = useCallback(() => {
		return detector.isSlowConnection(networkInfo);
	}, [detector, networkInfo]);

	return {
		networkInfo,
		isOnline: networkInfo.isOnline,
		isOffline: !networkInfo.isOnline,
		isSlowConnection: isSlowConnection(),
		checkConnectivity,
	};
}

// ============================================================================
// DEFAULT INSTANCE
// ============================================================================

export const networkDetector = new NetworkDetector();
