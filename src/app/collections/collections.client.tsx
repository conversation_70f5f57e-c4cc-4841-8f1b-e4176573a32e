'use client';

import { Button, Input, Label, LoadingSpinner, SearchInput, Translate } from '@/components/ui';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Collection, Language } from '@prisma/client';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowLeft, Check, Pencil, Plus, Trash2, X } from 'lucide-react';
import Link from 'next/link';
import React, { useCallback, useMemo, useState } from 'react';
import { CreateCollectionDialog } from './components/create-collection-dialog';
import { useCollections } from '@/hooks';
import { CollectionWithDetail } from '@/models';
import { DataFallback, NetworkFallback } from '@/components/fallback';
import { ErrorDisplay } from '@/components/ui/error-display';
import { useErrorHandler } from '@/contexts/error-context';

const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: { staggerChildren: 0.1 },
	},
};

const itemVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: { duration: 0.3 },
	},
};

export default function CollectionsClient() {
	const { t } = useTranslation();
	const [searchTerm, setSearchTerm] = useState('');
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [updateForm, setUpdateForm] = useState<{
		isOpen: boolean;
		name: string;
		collection: Collection | null;
	}>({
		isOpen: false,
		name: '',
		collection: null,
	});
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [currentCollection, setCurrentCollection] = useState<Collection | null>(null);
	const { collections, loading, error, createCollection, deleteCollection, updateCollection } =
		useCollections();
	const { handleError } = useErrorHandler('CollectionsClient');

	const filteredCollections = useMemo(
		() =>
			searchTerm.length > 0
				? collections?.filter((collection) =>
						collection.name.toLowerCase().includes(searchTerm.toLowerCase())
				  )
				: collections,
		[collections, searchTerm]
	);

	const handleEdit = useCallback((collection: Collection) => {
		setUpdateForm({
			isOpen: true,
			name: collection.name,
			collection,
		});
	}, []);

	const handleDelete = useCallback((collection: Collection) => {
		setCurrentCollection(collection);
		setIsDeleteDialogOpen(true);
	}, []);

	const confirmDelete = useCallback(async () => {
		if (!currentCollection) return;
		try {
			await deleteCollection(currentCollection.id);
			setIsDeleteDialogOpen(false);
		} catch (error) {
			console.error('Failed to delete collection:', error);
		}
	}, [currentCollection]);

	const handleOpenCreateDialog = useCallback(() => {
		setIsCreateDialogOpen(true);
	}, []);

	const handleCreateSubmit = useCallback(
		async (name: string, targetLanguage: Language, sourceLanguage: Language) => {
			if (!name.trim()) return;
			try {
				await createCollection(name, targetLanguage, sourceLanguage);
				setIsCreateDialogOpen(false);
			} catch (error) {
				console.error('Failed to create collection:', error);
			}
		},
		[]
	);

	const handleEditSubmit = useCallback(
		async (e: React.FormEvent) => {
			e.preventDefault();
			if (!updateForm.collection || !updateForm.name.trim()) return;
			try {
				await updateCollection(updateForm.collection.id, {
					name: updateForm.name,
				});
				setUpdateForm({
					isOpen: false,
					name: '',
					collection: null,
				});
			} catch (error) {
				handleError(error, 'update_collection', {
					collectionId: updateForm.collection?.id,
				});
			}
		},
		[updateForm]
	);

	const inputClass =
		'bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-primary/20 transition-all duration-200';

	const languageOptions = useMemo(() => {
		return Object.values(Language).map((lang) => ({
			value: lang,
			label: getTranslationKeyOfLanguage(lang),
		}));
	}, []);

	return (
		<NetworkFallback>
			<DataFallback
				isLoading={loading.fetch}
				error={error}
				onRetry={() => window.location.reload()}
				isEmpty={!collections || collections.length === 0}
				emptyMessage={t('collections.no_collections')}
			>
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ duration: 0.5 }}
					className="w-full sm:py-8"
				>
					<motion.div
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						className="flex items-center mb-6"
					>
						<Link href="/">
							<Button
								variant="ghost"
								size="sm"
								className="hover:bg-primary/10 transition-colors duration-200"
							>
								<ArrowLeft className="h-4 w-4" />
								<Translate text="ui.back" />
							</Button>
						</Link>
					</motion.div>

					<motion.div
						variants={containerVariants}
						initial="hidden"
						animate="visible"
						className="space-y-6"
					>
						<motion.div
							variants={itemVariants}
							className="flex justify-between items-center"
						>
							<h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
								<Translate text="headings.collections" />
							</h1>
							<Button
								onClick={handleOpenCreateDialog}
								className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300"
							>
								<Plus size={16} className="animate-pulse" />
								<Translate text="collections.create" />
							</Button>
						</motion.div>

						<motion.div variants={itemVariants}>
							<SearchInput
								value={searchTerm}
								onChange={setSearchTerm}
								onSearch={() => {}}
								placeholder={t('collections.search_placeholder')}
								className={inputClass}
							/>
						</motion.div>

						{/* Create Dialog */}
						<CreateCollectionDialog
							isOpen={isCreateDialogOpen}
							onOpenChange={setIsCreateDialogOpen}
							onSubmit={handleCreateSubmit}
							languageOptions={languageOptions}
							inputClass={inputClass}
							t={t}
						/>

						{error && (
							<div className="text-center text-destructive py-4">
								{typeof error === 'string'
									? error
									: error?.message || t('errors.collections_load_error')}
							</div>
						)}
						{!error && loading.fetch ? (
							<div className="flex justify-center items-center py-12">
								<LoadingSpinner size="lg" />
							</div>
						) : !error ? (
							<motion.div variants={containerVariants} className="space-y-4">
								{filteredCollections && filteredCollections.length > 0 ? (
									<AnimatePresence mode="popLayout">
										{filteredCollections.map((collection) => (
											<motion.div
												key={collection.id}
												variants={itemVariants}
												className="bg-card/50 backdrop-blur-sm border rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300"
												whileHover={{ scale: 1.01 }}
												whileTap={{ scale: 0.99 }}
												layout
											>
												<div className="flex items-center justify-between">
													<div className="flex-1">
														<Link
															href={`/collections/${collection.id}`}
														>
															<h3 className="text-lg font-medium cursor-pointer">
																{collection.name}
															</h3>
															<p className="text-sm text-muted-foreground">
																<Translate
																	text={getTranslationKeyOfLanguage(
																		collection.source_language
																	)}
																/>{' '}
																-{' '}
																<Translate
																	text={getTranslationKeyOfLanguage(
																		collection.target_language
																	)}
																/>
															</p>
														</Link>
													</div>
													<div className="flex items-center gap-2">
														<Button
															variant="ghost"
															size="sm"
															onClick={() => handleEdit(collection)}
															className="hover:bg-primary/10 hover:text-primary transition-colors duration-200"
														>
															<Pencil className="h-4 w-4" />
														</Button>
														<Button
															variant="ghost"
															size="sm"
															onClick={() => handleDelete(collection)}
															className="hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
														>
															<Trash2 className="h-4 w-4" />
														</Button>
													</div>
												</div>
											</motion.div>
										))}
									</AnimatePresence>
								) : (
									<motion.div
										variants={itemVariants}
										className="text-center py-12 border border-dashed rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors duration-300"
									>
										<h3 className="text-lg font-medium text-muted-foreground">
											<Translate text="collections.no_collections" />
										</h3>
										<Button
											onClick={handleOpenCreateDialog}
											className="mt-4 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300"
										>
											<Plus size={16} className="animate-pulse" />
											<Translate text="collections.create" />
										</Button>
									</motion.div>
								)}
							</motion.div>
						) : null}

						{/* Edit Dialog */}
						<Dialog
							open={updateForm.isOpen}
							onOpenChange={(open) =>
								setUpdateForm((prev) => ({ ...prev, isOpen: open }))
							}
						>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>
										<Translate text="collections.edit" />
									</DialogTitle>
								</DialogHeader>
								<form onSubmit={handleEditSubmit}>
									<div className="space-y-4">
										<div className="space-y-2">
											<Label htmlFor="edit-collection-name">
												<Translate text="collections.name" />
											</Label>
											<Input
												id="edit-collection-name"
												value={updateForm.name}
												onChange={(e) =>
													setUpdateForm((prev) => ({
														...prev,
														name: e.target.value,
													}))
												}
												placeholder={t('collections.name_placeholder')}
												className={inputClass}
											/>
										</div>
									</div>
									<DialogFooter className="mt-6">
										<Button
											type="button"
											variant="outline"
											onClick={() =>
												setUpdateForm((prev) => ({
													...prev,
													isOpen: false,
												}))
											}
										>
											<X size={16} className="mr-2" />
											<Translate text="ui.cancel" />
										</Button>
										<Button type="submit" disabled={!updateForm.name.trim()}>
											<Check size={16} className="mr-2" />
											<Translate text="ui.save" />
										</Button>
									</DialogFooter>
								</form>
							</DialogContent>
						</Dialog>

						{/* Delete Confirmation Dialog */}
						<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>
										<Translate text="collections.delete" />
									</DialogTitle>
									<DialogDescription>
										<Translate text="collections.delete_confirm" />
									</DialogDescription>
								</DialogHeader>
								<DialogFooter className="mt-6">
									<Button
										type="button"
										variant="outline"
										onClick={() => setIsDeleteDialogOpen(false)}
									>
										<X size={16} className="mr-2" />
										<Translate text="ui.cancel" />
									</Button>
									<Button
										type="button"
										variant="destructive"
										onClick={confirmDelete}
									>
										<Trash2 size={16} className="mr-2" />
										<Translate text="ui.delete" />
									</Button>
								</DialogFooter>
							</DialogContent>
						</Dialog>
					</motion.div>
				</motion.div>
			</DataFallback>
		</NetworkFallback>
	);
}
