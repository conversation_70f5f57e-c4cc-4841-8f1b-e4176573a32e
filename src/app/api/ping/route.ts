import { NextRequest, NextResponse } from 'next/server';

// ============================================================================
// PING ENDPOINT
// ============================================================================

/**
 * Simple ping endpoint for basic connectivity testing
 * Returns minimal response for load balancers and monitoring tools
 */

// GET /api/ping
export async function GET(): Promise<NextResponse> {
	return NextResponse.json(
		{ 
			status: 'ok', 
			timestamp: new Date().toISOString(),
			message: 'pong'
		},
		{ 
			status: 200,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
			}
		}
	);
}

// HEAD /api/ping
export async function HEAD(): Promise<NextResponse> {
	return new NextResponse(null, { 
		status: 200,
		headers: {
			'Cache-Control': 'no-cache, no-store, must-revalidate',
			'X-Status': 'ok',
		}
	});
}

// OPTIONS /api/ping
export async function OPTIONS(): Promise<NextResponse> {
	return new NextResponse(null, {
		status: 200,
		headers: {
			'Allow': 'GET, HEAD, OPTIONS',
			'Cache-Control': 'no-cache, no-store, must-revalidate',
		},
	});
}
